package com.writing.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.writing.entity.Chapter;

import java.util.List;
import java.util.Map;

/**
 * 章节服务接口
 */
public interface ChapterService extends IService<Chapter> {

    /**
     * 获取小说的章节列表（不包含章节内容，避免数据量过大）
     */
    List<Chapter> getChaptersByNovelId(Long novelId);

    /**
     * 分页获取小说的章节列表（不包含章节内容，避免数据量过大）
     */
    IPage<Chapter> getChaptersByNovelIdWithPage(Long novelId, Page<Chapter> page);

    /**
     * 获取章节内容
     */
    String getChapterContent(Long chapterId, Long novelId);

    /**
     * 获取章节详情
     */
    Chapter getChapterById(Long chapterId, Long novelId);

    /**
     * 创建章节
     */
    Chapter createChapter(Chapter chapter);

    /**
     * 更新章节
     */
    Chapter updateChapter(Chapter chapter);

    /**
     * 删除章节
     */
    boolean deleteChapter(Long chapterId, Long novelId);

    /**
     * 更新章节顺序
     */
    void updateChapterOrder(Long novelId, List<Map<String, Object>> orderData);

    /**
     * 计算章节字数
     */
    int calculateWordCount(String content);

    // 保留原有方法以保持兼容性


}
