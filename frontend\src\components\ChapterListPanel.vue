<template>
  <div class="panel-content">
    <div class="panel-header">
      <div class="panel-title">
        <span class="panel-icon">📝</span>
        <span>章节列表</span>
      </div>
      <el-dropdown @command="handleChapterCommand">
        <el-button size="small" type="primary">
          <el-icon><Plus /></el-icon>
          新增章节 <el-icon><ArrowDown /></el-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="manual">手动创建</el-dropdown-item>
            <el-dropdown-item command="ai-single">AI生成单章</el-dropdown-item>
            <el-dropdown-item command="ai-batch">AI批量生成</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>

    <div class="chapters-list" v-loading="loading" element-loading-text="正在加载章节..." :key="componentKey">
      <div
        v-for="(chapter, index) in chapters"
        :key="`chapter-${chapter.id}-${index}-${componentKey}`"
        class="chapter-item"
        :class="{ active: currentChapter?.id === chapter.id }"
        @click="selectChapter(chapter)"
      >
        <div class="chapter-info">
          <h4>第{{ getChapterNumber(index) }}章</h4>
          <p>{{ chapter.title }}</p>
          <div class="chapter-meta">
            <span>{{ chapter.wordCount || 0 }}字</span>
            <el-tag v-if="chapter.status" :type="getChapterStatusType(chapter.status)" size="small">
              {{ getChapterStatusText(chapter.status) }}
            </el-tag>
          </div>
          <el-tooltip
            v-if="chapter.description"
            :content="chapter.description"
            placement="top-start"
            :disabled="chapter.description.length <= 50"
            effect="light"
            :show-after="300"
          >
            <p class="chapter-desc chapter-desc-truncated">
              {{ chapter.description.length > 50 ? chapter.description.substring(0, 50) + '...' : chapter.description }}
            </p>
          </el-tooltip>
        </div>
        <div class="chapter-actions">
          <el-dropdown @command="(cmd) => handleChapterAction(cmd, chapter)">
            <el-button size="small" type="text">
              <el-icon><MoreFilled /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="edit">编辑信息</el-dropdown-item>
                <el-dropdown-item command="generate">AI生成正文</el-dropdown-item>
                <el-dropdown-item divided command="delete">删除</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>

      <div v-if="chapters.length === 0 && !loading" class="empty-chapters">
        <p>暂无章节</p>
        <el-button size="small" type="primary" @click="addNewChapter">
          创建第一章
        </el-button>
      </div>
    </div>

    <!-- 分页组件 -->
    <div v-if="pagination.total > 0" class="pagination-wrapper">
      <el-pagination
        size="small"
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.size"
        :page-sizes="[50, 100]"
        :total="pagination.total"
        layout="prev, pager, next"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits, watch, nextTick, ref } from 'vue'
import { Plus, ArrowDown, MoreFilled } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  chapters: {
    type: Array,
    default: () => []
  },
  currentChapter: {
    type: Object,
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  },
  pagination: {
    type: Object,
    default: () => ({
      current: 1,
      size: 10,
      total: 0
    })
  }
})

// Emits
const emit = defineEmits([
  'select-chapter',
  'chapter-command',
  'chapter-action',
  'add-new-chapter',
  'page-change',
  'size-change'
])

// 强制重新渲染的key
const componentKey = ref(0)

// 方法
const selectChapter = (chapter) => {
  emit('select-chapter', chapter)
}

const handleChapterCommand = (command) => {
  emit('chapter-command', command)
}

const handleChapterAction = (command, chapter) => {
  emit('chapter-action', command, chapter)
}

const addNewChapter = () => {
  emit('add-new-chapter')
}

const getChapterStatusType = (status) => {
  const statusMap = {
    draft: 'warning',
    completed: 'success',
    published: 'primary'
  }
  return statusMap[status] || 'warning'
}

const getChapterStatusText = (status) => {
  const statusMap = {
    draft: '草稿',
    completed: '完成',
    published: '发表'
  }
  return statusMap[status] || '草稿'
}

// 计算章节编号（考虑分页）
const getChapterNumber = (index) => {
  return (props.pagination.current - 1) * props.pagination.size + index + 1
}

// 分页相关方法
const handleSizeChange = (size) => {
  emit('size-change', size)
}

const handleCurrentChange = (current) => {
  emit('page-change', current)
}

// 监听章节列表变化，确保组件状态正确更新
watch(() => props.chapters, (newChapters, oldChapters) => {
  // 如果章节数量发生变化，强制更新组件
  if (newChapters.length !== oldChapters?.length) {
    componentKey.value++
    nextTick(() => {
      // 确保DOM已更新
    })
  }
}, { deep: true })

// 监听当前章节变化
watch(() => props.currentChapter, (newChapter, oldChapter) => {
  // 当前章节发生变化时，强制更新组件
  if (newChapter?.id !== oldChapter?.id) {
    componentKey.value++
    nextTick(() => {
      // 确保DOM已更新
    })
  }
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 13px;
  overflow: hidden;
  height: calc(100vh - 150px);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e4e7ed;
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.panel-icon {
  font-size: 18px;
}

.chapters-list {
  max-height: calc(100vh - 190px);
  overflow-y: auto;
}

.chapter-item {
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chapter-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
  transform: translateY(-1px);
}

.chapter-item.active {
  border-color: #409eff;
  background-color: #e6f4ff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.chapter-info {
  flex: 1;
}

.chapter-info h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  color: #303133;
}

.chapter-info p {
  margin: 0 0 4px 0;
  font-size: 13px;
  color: #606266;
  line-height: 1.4;
}

.chapter-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.chapter-desc {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.3;
}

.chapter-desc-truncated {
  cursor: help;
  transition: color 0.2s ease;
}

.chapter-desc-truncated:hover {
  color: #606266;
}

.chapter-actions {
  display: flex;
  gap: 4px;
}

.empty-chapters {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.pagination-wrapper {
  padding: 2px 0;
  display: flex;
  justify-content: center;
  margin-top: 10px;
}
</style>
